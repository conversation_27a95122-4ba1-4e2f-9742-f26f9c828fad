<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>梦羽AI绘图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .preview-container {
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #eee;
            border-radius: 5px;
            overflow: hidden;
        }
        .preview-image {
            max-width: 100%;
            max-height: 600px;
            display: none;
        }
        .loading {
            display: none;
            text-align: center;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        .log-container {
            height: 280px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-family: monospace;
        }
        #logText p {
            margin: 0;
            padding: 2px 0;
        }

        /* 兑换码管理样式 */
        .redemption-stats-card {
            transition: transform 0.2s ease-in-out;
        }

        .redemption-stats-card:hover {
            transform: translateY(-2px);
        }

        .code-item {
            transition: background-color 0.2s ease-in-out;
        }

        .code-item:hover {
            background-color: #f8f9fa;
        }

        .badge {
            font-size: 0.75em;
        }

        .dropdown-toggle::after {
            display: none;
        }

        .toast {
            min-width: 300px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .redemption-stats-card .card-body {
                padding: 1rem 0.5rem;
            }

            .code-item .row > div {
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 用户信息栏 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div id="userSection">
                            {% if current_user %}
                            <!-- 已登录用户显示 -->
                            <div id="loggedInSection">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h5 class="mb-0">欢迎，{{ current_user.username }}！</h5>
                                        <small class="text-muted">当前积分：<span id="userPoints">{{ current_user.points }}</span> 点</small>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button id="logoutBtn" class="btn btn-outline-secondary btn-sm">退出登录</button>
                                        {% if current_user.is_admin %}
                                        <button id="adminBtn" class="btn btn-outline-primary btn-sm ms-2">管理面板</button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <!-- 未登录用户显示 -->
                            <div id="notLoggedInSection">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="mb-0">请登录以使用AI绘图功能</h5>
                                        <small class="text-muted">新用户注册即送10积分，每次生成消耗1积分</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button id="showLoginBtn" class="btn btn-primary btn-sm me-2">登录</button>
                                        <button id="showRegisterBtn" class="btn btn-outline-primary btn-sm">注册</button>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        提示词
                    </div>
                    <div class="card-body">
                        <div class="d-flex mb-2">
                            <textarea id="promptInput" class="form-control" rows="5" placeholder="输入提示词..."></textarea>
                            <button id="randomTagBtn" class="btn btn-outline-secondary ms-2">从随机词库导入</button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        反向提示词
                    </div>
                    <div class="card-body">
                        <textarea id="negativePromptInput" class="form-control" rows="3">lowres, {bad}, error, fewer, extra, missing, worst quality, jpeg artifacts, bad quality, watermark, unfinished, displeasing, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]</textarea>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        参数设置
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="widthInput" class="form-label">宽度:</label>
                                <input type="number" id="widthInput" class="form-control" value="832" min="512" max="1024" step="64">
                            </div>
                            <div class="col-6">
                                <label for="heightInput" class="form-label">高度:</label>
                                <input type="number" id="heightInput" class="form-control" value="1216" min="512" max="1024" step="64">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="stepsInput" class="form-label">步数:</label>
                                <input type="number" id="stepsInput" class="form-control" value="28" min="20" max="50">
                            </div>
                            <div class="col-6">
                                <label for="cfgInput" class="form-label">CFG:</label>
                                <input type="number" id="cfgInput" class="form-control" value="7" min="1" max="30">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="modelSelect" class="form-label">模型:</label>
                            <select id="modelSelect" class="form-select">
                                {% for model in models %}
                                <option value="{{ loop.index0 }}" data-proxy="{{ model.proxy_url }}" data-type="{{ model.type }}">{{ model.display_name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="appendDefaultPromptCheck" checked>
                                <label class="form-check-label" for="appendDefaultPromptCheck">
                                    附加推荐质量提示词
                                </label>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableHrCheck" checked>
                                    <label class="form-check-label" for="enableHrCheck">
                                        使用负面词条
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="randomSeedCheck" checked>
                                    <label class="form-check-label" for="randomSeedCheck">
                                        随机种子
                                    </label>
                                </div>
                            </div>
                        </div>



                        <div class="mb-3">
                            <label for="seedInput" class="form-label">种子:</label>
                            <input type="number" id="seedInput" class="form-control" value="0" min="-1" max="2147483647">
                        </div>

                        <!-- 视频生成专用参数 -->
                        <div id="videoParams" style="display: none;">
                            <div class="mb-3">
                                <label for="imagePathInput" class="form-label">输入图片URL:</label>
                                <div class="input-group">
                                    <input type="text" id="imagePathInput" class="form-control" value="" placeholder="输入图片URL...">
                                    <button class="btn btn-outline-secondary" type="button" id="uploadImageBtn">本地上传</button>
                                    <button class="btn btn-outline-secondary" type="button" id="pasteImageBtn">粘贴图片</button>
                                    <input type="file" id="imageFileInput" style="display: none;" accept="image/*">
                                </div>
                                <div class="mt-2">
                                    <img id="imagePreview" class="img-thumbnail" style="max-height: 150px; max-width: 150px; display: none;" alt="图片预览">
                                    <span id="imageStatusText"></span>
                                </div>
                                <div id="directUrlResult" class="mt-2" style="display: none;">
                                    <div class="alert alert-success">
                                        <strong>直连URL已生成：</strong>
                                        <div class="input-group mt-2">
                                            <input type="text" id="directUrlText" class="form-control" readonly>
                                            <button class="btn btn-outline-secondary" type="button" id="copyDirectUrlBtn">复制</button>
                                            <button class="btn btn-outline-secondary" type="button" id="useDirectUrlBtn">使用此URL</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3" style="display: none;">
                                <div class="col-6">
                                    <label for="motionBucketInput" class="form-label">运动强度:</label>
                                    <input type="number" id="motionBucketInput" class="form-control" value="2" min="1" max="10">
                                </div>
                                <div class="col-6">
                                    <label for="condAugInput" class="form-label">条件增强:</label>
                                    <input type="number" id="condAugInput" class="form-control" value="1" min="0" max="5">
                                </div>
                            </div>
                        </div>

                        <!-- 兑换码区域 -->
                        <div class="card mb-3" id="redemptionSection" style="display: none;">
                            <div class="card-header">
                                <small>兑换码</small>
                            </div>
                            <div class="card-body">
                                <div class="input-group">
                                    <input type="text" id="redemptionCodeInput" class="form-control" placeholder="输入兑换码获取积分" maxlength="20">
                                    <button id="redeemBtn" class="btn btn-outline-primary">兑换</button>
                                </div>
                                <div class="form-text">输入兑换码可以获得积分奖励</div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button id="generateBtn" class="btn btn-primary">生成图像</button>
                            <button id="clearLogBtn" class="btn btn-outline-danger">清除日志</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧预览区域 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <span id="previewTitle">图片预览</span>
                    </div>
                    <div class="card-body">
                        <div class="preview-container">
                            <div id="loadingIndicator" class="loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2" id="loadingText">正在生成图片，请稍候...</p>
                            </div>
                            <img id="previewImage" class="preview-image" alt="生成的图片将在此显示">
                            <video id="previewVideo" class="preview-image" controls style="display: none;" alt="生成的视频将在此显示">
                                您的浏览器不支持视频播放。
                            </video>
                            <div id="previewPlaceholder">生成内容后将在此显示</div>
                        </div>
                        <div class="d-grid mt-3">
                            <a id="downloadBtn" class="btn btn-success" download disabled>保存文件</a>
                            <button id="openUrlBtn" class="btn btn-secondary mt-2" disabled>在浏览器中查看原文件</button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        日志
                    </div>
                    <div class="card-body">
                        <div id="logContainer" class="log-container">
                            <div id="logText">
                                <p>这是梦羽的绘图工具服务端版,后端使用H200作图</p>
                                <p>您对测试生成的内容负全部责任。</p>
                                <p>系统已配置代理池，所有请求将通过管理员配置的代理池进行处理。</p>
                                <p>有限的技术支持和反馈问题提交可以联系QQ:1031029814</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div id="loginMessage" class="alert" style="display: none;"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="loginForm" class="btn btn-primary">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required>
                            <div class="form-text">至少3个字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                            <div class="form-text">至少6个字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">邮箱（可选）</label>
                            <input type="email" class="form-control" id="registerEmail">
                        </div>
                        <div id="registerMessage" class="alert" style="display: none;"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="registerForm" class="btn btn-primary">注册</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员面板模态框 -->
    <div class="modal fade" id="adminModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">管理员面板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="adminTabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#usersTab">用户管理</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#userApprovalTab">用户审核</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#statisticsTab">系统统计</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#redemptionTab">兑换码管理</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#settingsTab">系统设置</a>
                        </li>
                    </ul>
                    <div class="tab-content mt-3">
                        <div class="tab-pane fade show active" id="usersTab">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <h6>用户积分充值</h6>
                                    <div class="row">
                                        <div class="col-4">
                                            <input type="text" id="chargeUsername" class="form-control" placeholder="用户名">
                                        </div>
                                        <div class="col-3">
                                            <input type="number" id="chargePoints" class="form-control" placeholder="积分数" min="1">
                                        </div>
                                        <div class="col-3">
                                            <input type="text" id="chargeReason" class="form-control" placeholder="充值原因">
                                        </div>
                                        <div class="col-2">
                                            <button id="chargeBtn" class="btn btn-primary btn-sm">充值</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="usersTable">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="userApprovalTab">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <h6>待审核用户</h6>
                                </div>
                                <div class="col-md-4">
                                    <button id="refreshPendingUsersBtn" class="btn btn-outline-primary btn-sm">刷新</button>
                                </div>
                            </div>
                            <div id="pendingUsersTable">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="statisticsTab">
                            <div id="statisticsContent">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="redemptionTab">
                            <!-- 兑换码管理头部 -->
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0">
                                    <i class="fas fa-ticket-alt me-2"></i>兑换码管理
                                </h5>
                                <div class="btn-group">
                                    <button id="createCodeBtn" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>创建兑换码
                                    </button>
                                    <button id="batchOperationBtn" class="btn btn-outline-secondary">
                                        <i class="fas fa-tasks me-1"></i>批量操作
                                    </button>
                                    <button id="exportCodesBtn" class="btn btn-outline-info">
                                        <i class="fas fa-download me-1"></i>导出
                                    </button>
                                </div>
                            </div>

                            <!-- 统计卡片区域 -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                                    <i class="fas fa-ticket-alt text-primary fs-4"></i>
                                                </div>
                                            </div>
                                            <h4 class="mb-1" id="totalCodesCount">-</h4>
                                            <p class="text-muted mb-0 small">总兑换码数</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                                    <i class="fas fa-check-circle text-success fs-4"></i>
                                                </div>
                                            </div>
                                            <h4 class="mb-1" id="activeCodesCount">-</h4>
                                            <p class="text-muted mb-0 small">有效兑换码</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                                    <i class="fas fa-clock text-warning fs-4"></i>
                                                </div>
                                            </div>
                                            <h4 class="mb-1" id="usedCodesCount">-</h4>
                                            <p class="text-muted mb-0 small">已使用</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                                    <i class="fas fa-coins text-info fs-4"></i>
                                                </div>
                                            </div>
                                            <h4 class="mb-1" id="totalPointsDistributed">-</h4>
                                            <p class="text-muted mb-0 small">总发放积分</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 搜索和筛选区域 -->
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label small">搜索兑换码</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-search"></i>
                                                </span>
                                                <input type="text" id="searchCodeInput" class="form-control" placeholder="输入兑换码或描述">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">类型筛选</label>
                                            <select id="filterCodeType" class="form-select">
                                                <option value="">全部类型</option>
                                                <option value="one_time">一次性</option>
                                                <option value="activity">活动码</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">状态筛选</label>
                                            <select id="filterCodeStatus" class="form-select">
                                                <option value="">全部状态</option>
                                                <option value="active">有效</option>
                                                <option value="inactive">禁用</option>
                                                <option value="expired">过期</option>
                                                <option value="used">已用完</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">积分范围</label>
                                            <select id="filterPointsRange" class="form-select">
                                                <option value="">全部积分</option>
                                                <option value="1-10">1-10积分</option>
                                                <option value="11-50">11-50积分</option>
                                                <option value="51-100">51-100积分</option>
                                                <option value="100+">100+积分</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label small">操作</label>
                                            <div class="d-flex gap-2">
                                                <button id="applyFiltersBtn" class="btn btn-outline-primary btn-sm flex-fill">
                                                    <i class="fas fa-filter me-1"></i>筛选
                                                </button>
                                                <button id="clearFiltersBtn" class="btn btn-outline-secondary btn-sm flex-fill">
                                                    <i class="fas fa-times me-1"></i>清除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 兑换码列表 -->
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-white border-bottom">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-list me-2"></i>兑换码列表
                                            <span id="codesCountBadge" class="badge bg-secondary ms-2">0</span>
                                        </h6>
                                        <div class="d-flex gap-2">
                                            <div class="btn-group btn-group-sm">
                                                <button id="selectAllCodesBtn" class="btn btn-outline-secondary">
                                                    <i class="fas fa-check-square me-1"></i>全选
                                                </button>
                                                <button id="deselectAllCodesBtn" class="btn btn-outline-secondary">
                                                    <i class="fas fa-square me-1"></i>取消
                                                </button>
                                            </div>
                                            <button id="refreshCodesBtn" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-sync-alt me-1"></i>刷新
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <div id="redemptionCodesContainer" style="max-height: 500px; overflow-y: auto;">
                                        <div class="text-center p-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 text-muted">加载中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 最近使用记录 -->
                            <div class="card border-0 shadow-sm mt-4">
                                <div class="card-header bg-white border-bottom">
                                    <h6 class="mb-0">
                                        <i class="fas fa-history me-2"></i>最近使用记录
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div id="recentRedemptionsContainer" style="max-height: 300px; overflow-y: auto;">
                                        <div class="text-center p-4">
                                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 text-muted small">加载中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="settingsTab">
                            <div class="row">
                                <div class="col-12">
                                    <h6>代理池设置</h6>
                                    <div class="mb-3">
                                        <label for="globalProxyApiUrl" class="form-label">全局代理池API地址:</label>
                                        <input type="text" id="globalProxyApiUrl" class="form-control" placeholder="输入代理池API地址...">
                                        <div class="form-text">设置后所有用户将使用此代理池，无需用户单独配置</div>
                                    </div>
                                    <button id="saveProxySettingsBtn" class="btn btn-primary">保存代理池设置</button>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <h6>积分设置</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <label for="imageGenerationCost" class="form-label">图片生成消耗积分:</label>
                                            <input type="number" id="imageGenerationCost" class="form-control" min="1" value="1">
                                        </div>
                                        <div class="col-6">
                                            <label for="videoGenerationCost" class="form-label">视频生成消耗积分:</label>
                                            <input type="number" id="videoGenerationCost" class="form-control" min="1" value="5">
                                        </div>
                                    </div>
                                    <button id="savePointsSettingsBtn" class="btn btn-primary mt-2">保存积分设置</button>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <h6>用户注册设置</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireRegistrationApproval">
                                        <label class="form-check-label" for="requireRegistrationApproval">
                                            新用户注册需要管理员审核
                                        </label>
                                        <div class="form-text">开启后，新注册的用户需要等待管理员审核通过才能使用系统</div>
                                    </div>
                                    <button id="saveRegistrationSettingsBtn" class="btn btn-primary mt-2">保存注册设置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建兑换码模态框 -->
    <div class="modal fade" id="createCodeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>创建兑换码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createCodeForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="newCodeType" class="form-label">
                                    <i class="fas fa-tag me-1"></i>兑换码类型
                                </label>
                                <select id="newCodeType" class="form-select" required>
                                    <option value="one_time">一次性兑换码</option>
                                    <option value="activity">活动兑换码</option>
                                </select>
                                <div class="form-text">
                                    一次性：使用一次后失效；活动码：每个用户只能使用一次
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="newCodePoints" class="form-label">
                                    <i class="fas fa-coins me-1"></i>积分数量
                                </label>
                                <input type="number" id="newCodePoints" class="form-control" value="10" min="1" max="10000" required>
                                <div class="form-text">每个兑换码可获得的积分数量</div>
                            </div>
                            <div class="col-md-6">
                                <label for="newCodeCount" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>生成数量
                                </label>
                                <input type="number" id="newCodeCount" class="form-control" value="1" min="1" max="1000" required>
                                <div class="form-text">一次生成的兑换码数量</div>
                            </div>
                            <div class="col-md-6">
                                <label for="newCodeExpireDays" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>有效天数
                                </label>
                                <input type="number" id="newCodeExpireDays" class="form-control" value="30" min="1" max="365" required>
                                <div class="form-text">兑换码的有效期（天）</div>
                            </div>
                            <div class="col-12">
                                <label for="newCodeDescription" class="form-label">
                                    <i class="fas fa-comment me-1"></i>描述信息
                                </label>
                                <input type="text" id="newCodeDescription" class="form-control" placeholder="输入兑换码的用途或活动名称">
                                <div class="form-text">可选，用于标识兑换码的用途</div>
                            </div>
                        </div>

                        <!-- 快速模板 -->
                        <div class="mt-4">
                            <h6 class="mb-3">
                                <i class="fas fa-magic me-2"></i>快速模板
                            </h6>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="applyCodeTemplate('welcome')">
                                        新用户奖励<br><small>10积分·30天</small>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="applyCodeTemplate('activity')">
                                        活动奖励<br><small>50积分·7天</small>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="applyCodeTemplate('vip')">
                                        VIP福利<br><small>100积分·60天</small>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="applyCodeTemplate('test')">
                                        测试用码<br><small>1积分·1天</small>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 预览信息 -->
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>生成预览
                                </h6>
                                <p class="mb-1">
                                    将生成 <strong id="previewCount">1</strong> 个
                                    <strong id="previewType">一次性</strong>兑换码
                                </p>
                                <p class="mb-1">
                                    每个兑换码价值 <strong id="previewPoints">10</strong> 积分
                                </p>
                                <p class="mb-1">
                                    有效期至 <strong id="previewExpireDate">-</strong>
                                </p>
                                <p class="mb-0">
                                    总价值：<strong id="previewTotalPoints">10</strong> 积分
                                </p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" id="confirmCreateCodeBtn" class="btn btn-primary">
                        <i class="fas fa-magic me-1"></i>生成兑换码
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal fade" id="batchOperationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-tasks me-2"></i>批量操作
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>已选择 <strong id="selectedCodesCount">0</strong> 个兑换码</p>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" id="batchActivateBtn">
                            <i class="fas fa-check-circle me-2"></i>批量启用
                        </button>
                        <button type="button" class="btn btn-warning" id="batchDeactivateBtn">
                            <i class="fas fa-pause-circle me-2"></i>批量禁用
                        </button>
                        <button type="button" class="btn btn-danger" id="batchDeleteBtn">
                            <i class="fas fa-trash me-2"></i>批量删除
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 统一使用代理池，无需检查配置状态

            // 当前用户状态
            let currentUser = {% if current_user %}{{ current_user | tojson }}{% else %}null{% endif %};

            // 元素引用
            const promptInput = document.getElementById('promptInput');
            const negativePromptInput = document.getElementById('negativePromptInput');
            const widthInput = document.getElementById('widthInput');
            const heightInput = document.getElementById('heightInput');
            const stepsInput = document.getElementById('stepsInput');
            const cfgInput = document.getElementById('cfgInput');
            const modelSelect = document.getElementById('modelSelect');
            const appendDefaultPromptCheck = document.getElementById('appendDefaultPromptCheck');
            const enableHrCheck = document.getElementById('enableHrCheck');
            const randomSeedCheck = document.getElementById('randomSeedCheck');

            const seedInput = document.getElementById('seedInput');
            const generateBtn = document.getElementById('generateBtn');
            const openUrlBtn = document.getElementById('openUrlBtn');
            const clearLogBtn = document.getElementById('clearLogBtn');
            const randomTagBtn = document.getElementById('randomTagBtn');
            const logText = document.getElementById('logText');
            const previewImage = document.getElementById('previewImage');
            const previewVideo = document.getElementById('previewVideo');
            const previewPlaceholder = document.getElementById('previewPlaceholder');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadingText = document.getElementById('loadingText');
            const previewTitle = document.getElementById('previewTitle');
            const downloadBtn = document.getElementById('downloadBtn');
            const videoParams = document.getElementById('videoParams');
            const imagePathInput = document.getElementById('imagePathInput');
            const motionBucketInput = document.getElementById('motionBucketInput');
            const condAugInput = document.getElementById('condAugInput');
            const imagePreview = document.getElementById('imagePreview');
            const imageStatusText = document.getElementById('imageStatusText');
            const uploadImageBtn = document.getElementById('uploadImageBtn');
            const pasteImageBtn = document.getElementById('pasteImageBtn');
            const imageFileInput = document.getElementById('imageFileInput');
            const directUrlResult = document.getElementById('directUrlResult');
            const directUrlText = document.getElementById('directUrlText');
            const copyDirectUrlBtn = document.getElementById('copyDirectUrlBtn');
            const useDirectUrlBtn = document.getElementById('useDirectUrlBtn');

            // 当前图片信息
            let currentImageUrl = null;
            let originalImageUrl = null;

            // 用户认证相关元素
            const showLoginBtn = document.getElementById('showLoginBtn');
            const showRegisterBtn = document.getElementById('showRegisterBtn');
            const logoutBtn = document.getElementById('logoutBtn');
            const adminBtn = document.getElementById('adminBtn');
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
            const adminModal = new bootstrap.Modal(document.getElementById('adminModal'));
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            const userPointsSpan = document.getElementById('userPoints');

            // 添加日志函数
            function log(message) {
                const p = document.createElement('p');
                p.textContent = message;
                logText.appendChild(p);
                const logContainer = document.getElementById('logContainer');
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            // 更新用户界面
            function updateUserInterface(user) {
                currentUser = user;
                if (user) {
                    // 更新积分显示
                    if (userPointsSpan) {
                        userPointsSpan.textContent = user.points;
                    }

                    // 显示已登录状态
                    const loggedInSection = document.getElementById('loggedInSection');
                    const notLoggedInSection = document.getElementById('notLoggedInSection');
                    if (loggedInSection && notLoggedInSection) {
                        loggedInSection.style.display = 'block';
                        notLoggedInSection.style.display = 'none';

                        // 更新用户名显示
                        const usernameSpan = loggedInSection.querySelector('h5');
                        if (usernameSpan) {
                            usernameSpan.textContent = `欢迎，${user.username}！`;
                        }

                        // 显示兑换码区域
                        const redemptionSection = document.getElementById('redemptionSection');
                        if (redemptionSection) {
                            redemptionSection.style.display = 'block';
                        }
                    }
                } else {
                    // 显示未登录状态
                    const loggedInSection = document.getElementById('loggedInSection');
                    const notLoggedInSection = document.getElementById('notLoggedInSection');
                    if (loggedInSection && notLoggedInSection) {
                        loggedInSection.style.display = 'none';
                        notLoggedInSection.style.display = 'block';
                    }
                }
            }

            // 检查用户是否已登录
            function checkLoginStatus() {
                if (!currentUser) {
                    log('请先登录后再使用生成功能');
                    if (showLoginBtn) {
                        showLoginBtn.click();
                    }
                    return false;
                }

                if (currentUser.points <= 0) {
                    log('积分不足，请联系管理员充值');
                    return false;
                }

                return true;
            }

            // 自动上传图片到图床并获取直连URL
            function autoUploadImageToHost(imageDataUrl) {
                log('正在自动上传图片获取直连URL...');

                // 将图片转换为Blob（不压缩）
                function dataURLtoBlob(dataURL) {
                    const arr = dataURL.split(',');
                    const mime = arr[0].match(/:(.*?);/)[1];
                    const bstr = atob(arr[1]);
                    let n = bstr.length;
                    const u8arr = new Uint8Array(n);
                    while (n--) {
                        u8arr[n] = bstr.charCodeAt(n);
                    }
                    return new Blob([u8arr], { type: mime });
                }

                // 直接转换图片为Blob（不压缩）
                const imageBlob = dataURLtoBlob(imageDataUrl);
                const sizeMB = imageBlob.size / (1024 * 1024);
                log(`图片大小: ${sizeMB.toFixed(2)}MB`);

                // 准备表单数据 - 直接使用原始图片数据
                const formData = new FormData();
                formData.append('file', imageBlob, `miaomiao_${Date.now()}.png`);

                // 直接发送到ggexacg-imger-55.deno.dev
                return fetch('https://ggexacg-imger-55.deno.dev/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    log('收到响应:', JSON.stringify(data));

                    // 解析ggexacg-imger-55.deno.dev的响应格式
                    if (Array.isArray(data) && data.length > 0) {
                        const uploadInfo = data[0];
                        // 检查是否有URL且状态为成功状态之一
                        if (uploadInfo && uploadInfo.url &&
                            (uploadInfo.status === 'uploaded' || uploadInfo.status === 'cached' || uploadInfo.status === 'overwritten')) {

                            let statusMessage = '';
                            switch(uploadInfo.status) {
                                case 'uploaded':
                                    statusMessage = '图片上传成功！';
                                    break;
                                case 'cached':
                                    statusMessage = '图片已存在，使用缓存版本！';
                                    break;
                                case 'overwritten':
                                    statusMessage = '图片已更新！';
                                    break;
                            }

                            log(statusMessage);
                            log(`直连URL：${uploadInfo.url}`);

                            // 自动填入URL到输入框
                            if (imagePathInput) {
                                imagePathInput.value = uploadInfo.url;
                            }

                            return uploadInfo.url;
                        } else {
                            throw new Error(`图片处理失败：${uploadInfo ? uploadInfo.status : '未知状态'}`);
                        }
                    } else {
                        throw new Error('图片上传失败：返回数据格式不正确');
                    }
                })
                .catch(error => {
                    log(`上传过程中发生错误：${error.message}`);
                    console.error('上传错误详情:', error);
                    throw error;
                });
            }

            // 清除日志
            clearLogBtn.addEventListener('click', function() {
                logText.innerHTML = '';
            });

            // 显示登录模态框
            if (showLoginBtn) {
                showLoginBtn.addEventListener('click', function() {
                    loginModal.show();
                });
            }

            // 显示注册模态框
            if (showRegisterBtn) {
                showRegisterBtn.addEventListener('click', function() {
                    registerModal.show();
                });
            }

            // 退出登录
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    fetch('/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            log(data.message);
                            updateUserInterface(null);
                            // 刷新页面以更新服务器端状态
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        }
                    })
                    .catch(error => {
                        log(`退出登录时发生错误: ${error}`);
                    });
                });
            }

            // 登录表单提交
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const username = document.getElementById('loginUsername').value.trim();
                    const password = document.getElementById('loginPassword').value.trim();
                    const messageDiv = document.getElementById('loginMessage');

                    if (!username || !password) {
                        messageDiv.className = 'alert alert-danger';
                        messageDiv.textContent = '请填写用户名和密码';
                        messageDiv.style.display = 'block';
                        return;
                    }

                    fetch('/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            messageDiv.className = 'alert alert-success';
                            messageDiv.textContent = data.message;
                            messageDiv.style.display = 'block';

                            log(`登录成功！欢迎 ${data.user.username}，当前积分：${data.user.points}`);

                            setTimeout(() => {
                                loginModal.hide();
                                updateUserInterface(data.user);
                                // 刷新页面以更新服务器端状态
                                window.location.reload();
                            }, 1000);
                        } else {
                            messageDiv.className = 'alert alert-danger';
                            messageDiv.textContent = data.message;
                            messageDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        messageDiv.className = 'alert alert-danger';
                        messageDiv.textContent = `登录时发生错误: ${error}`;
                        messageDiv.style.display = 'block';
                    });
                });
            }

            // 注册表单提交
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const username = document.getElementById('registerUsername').value.trim();
                    const password = document.getElementById('registerPassword').value.trim();
                    const email = document.getElementById('registerEmail').value.trim();
                    const messageDiv = document.getElementById('registerMessage');

                    if (!username || !password) {
                        messageDiv.className = 'alert alert-danger';
                        messageDiv.textContent = '请填写用户名和密码';
                        messageDiv.style.display = 'block';
                        return;
                    }

                    if (username.length < 3) {
                        messageDiv.className = 'alert alert-danger';
                        messageDiv.textContent = '用户名至少需要3个字符';
                        messageDiv.style.display = 'block';
                        return;
                    }

                    if (password.length < 6) {
                        messageDiv.className = 'alert alert-danger';
                        messageDiv.textContent = '密码至少需要6个字符';
                        messageDiv.style.display = 'block';
                        return;
                    }

                    fetch('/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password,
                            email: email
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            messageDiv.className = 'alert alert-success';
                            messageDiv.textContent = data.message;
                            messageDiv.style.display = 'block';

                            if (data.require_approval) {
                                // 需要审核的情况
                                log(`注册成功！请等待管理员审核`);
                                setTimeout(() => {
                                    registerModal.hide();
                                }, 2000);
                            } else {
                                // 不需要审核，直接登录
                                log(`注册成功！欢迎 ${data.user.username}，获得 ${data.user.points} 积分`);
                                setTimeout(() => {
                                    registerModal.hide();
                                    updateUserInterface(data.user);
                                    // 刷新页面以更新服务器端状态
                                    window.location.reload();
                                }, 1000);
                            }
                        } else {
                            messageDiv.className = 'alert alert-danger';
                            messageDiv.textContent = data.message;
                            messageDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        messageDiv.className = 'alert alert-danger';
                        messageDiv.textContent = `注册时发生错误: ${error}`;
                        messageDiv.style.display = 'block';
                    });
                });
            }

            // 获取随机标签
            randomTagBtn.addEventListener('click', function() {
                fetch('/random_tag')
                    .then(response => response.json())
                    .then(data => {
                        promptInput.value = data.tag;
                        log(`已随机导入提示词: ${data.tag}`);
                    })
                    .catch(error => {
                        log(`导入随机提示词时发生错误: ${error}`);
                    });
            });

            // 检查视频模型队列状态的函数
            function checkVideoModelQueueStatus(modelIndex) {
                fetch(`/video_queue_status?model_index=${modelIndex}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            log(`队列状态检查错误: ${data.error}`);
                        } else if (data.is_processing) {
                            log(`⚠️ 模型 "${data.model_name}" 当前正在排队中，已等待 ${data.elapsed_time} 秒`);
                            log(`💡 建议：${data.message}`);
                        } else {
                            log(`✅ 模型 "${data.model_name}" 队列空闲，可以开始生成`);
                        }
                    })
                    .catch(error => {
                        log(`检查队列状态时发生错误: ${error}`);
                    });
            }

            // 模型变更处理
            modelSelect.addEventListener('change', function() {
                const selectedOption = modelSelect.options[modelSelect.selectedIndex];
                const hasProxyUrl = selectedOption.dataset.proxy !== '';
                const modelType = selectedOption.dataset.type;
                const modelIndex = modelSelect.value;

                // 根据模型类型显示/隐藏相应的参数
                if (modelType === 'video') {
                    videoParams.style.display = 'block';
                    previewTitle.textContent = '视频预览';
                    generateBtn.textContent = '生成视频';
                    // 为视频模型设置默认参数
                    widthInput.value = 512;;
                    heightInput.value = 896;
                    stepsInput.value = 4;
                    //视频模式下高度宽度步数cfg禁用
                    widthInput.disabled = true;
                    heightInput.disabled = true;
                    stepsInput.disabled = true;
                    cfgInput.disabled = true;
                    promptInput.placeholder = '输入视频描述提示词，例如：make this image come alive, cinematic motion, smooth animation';

                    // 检查视频模型队列状态
                    log(`🔍 正在检查模型 "${selectedOption.textContent}" 的队列状态...`);
                    checkVideoModelQueueStatus(modelIndex);
                    negativePromptInput.value = 'Bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards, watermark, text, signature';
                } else {
                    videoParams.style.display = 'none';
                    //图片模式下高度宽度步数cfg重新启用
                    widthInput.disabled = false;
                    heightInput.disabled = false;
                    stepsInput.disabled = false;
                    cfgInput.disabled = false;
                    previewTitle.textContent = '图片预览';
                    generateBtn.textContent = '生成图像';
                    // 为图像模型设置默认参数
                    widthInput.value = 832;
                    heightInput.value = 1216;
                    stepsInput.value = 28;
                    promptInput.placeholder = '输入提示词...';
                    negativePromptInput.value = 'lowres, {bad}, error, fewer, extra, missing, worst quality, jpeg artifacts, bad quality, watermark, unfinished, displeasing, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]';
                }

                // 统一使用管理员配置的代理池，无需用户选择
                log(`已选择 ${selectedOption.text}，将使用管理员配置的代理池`);
            });

            // 生成内容（图像或视频）
            generateBtn.addEventListener('click', function() {
                // 检查登录状态
                if (!checkLoginStatus()) {
                    return;
                }

                const prompt = promptInput.value.trim();
                const selectedOption = modelSelect.options[modelSelect.selectedIndex];
                const modelType = selectedOption.dataset.type;

                if (!prompt) {
                    log("错误：提示词不能为空!");
                    return;
                }

                // 禁用生成按钮
                generateBtn.disabled = true;
                openUrlBtn.disabled = true;
                downloadBtn.disabled = true;

                // 显示加载指示器
                previewPlaceholder.style.display = 'none';
                previewImage.style.display = 'none';
                previewVideo.style.display = 'none';
                loadingIndicator.style.display = 'block';

                // 根据模型类型设置加载文本
                if (modelType === 'video') {
                    loadingText.textContent = '正在生成视频，请稍候...（视频生成需要较长时间，约1-3分钟）';
                } else {
                    loadingText.textContent = '正在生成图片，请稍候...';
                }

                // 准备表单数据
                const formData = new FormData();
                formData.append('prompt', prompt);
                formData.append('negative_prompt', negativePromptInput.value.trim());
                formData.append('width', widthInput.value);
                formData.append('height', heightInput.value);
                formData.append('steps', stepsInput.value);
                formData.append('seed', seedInput.value);
                // 统一使用管理员配置的代理池，无需传递use_proxy_address参数

                // 根据模型类型添加不同的参数
                let endpoint = '/generate';
                if (modelType === 'video') {
                    // 视频生成参数
                    // 优先使用直连URL，如果没有URL则使用base64数据
                    let imageSource = '';
                    if (imagePathInput.value.trim() !== '') {
                        imageSource = imagePathInput.value.trim(); // 优先使用URL
                        log("使用直连URL...");
                    } else if (imagePreview.style.display !== 'none') {
                        imageSource = imagePreview.src; // 备用：使用预览图片的base64数据
                        log("使用上传/粘贴的图片base64数据...");
                    }

                    if (!imageSource) {
                        log("错误：未提供图片源！请上传图片或提供图片URL。");
                        generateBtn.disabled = false;
                        openUrlBtn.disabled = false;
                        downloadBtn.disabled = false;
                        loadingIndicator.style.display = 'none';
                        previewPlaceholder.style.display = 'block';
                        return;
                    }

                    formData.append('image_source', imageSource);
                    formData.append('motion_bucket_id', motionBucketInput.value);
                    formData.append('cond_aug', condAugInput.value);
                    formData.append('model_index', modelSelect.value);  // 添加模型索引
                    endpoint = '/generate_video';
                    log(`开始生成视频，使用模型: ${selectedOption.textContent}...`);
                } else {
                    // 图像生成参数
                    formData.append('cfg', cfgInput.value);
                    formData.append('enable_hr', enableHrCheck.checked);
                    formData.append('restore_faces', randomSeedCheck.checked);
                    formData.append('model_index', modelSelect.value);
                    formData.append('append_default_prompt', appendDefaultPromptCheck.checked);
                    log("开始生成图像...");
                }

                // 发送请求
                fetch(endpoint, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        log(data.message);

                        // 更新用户积分显示
                        if (data.user_points !== undefined && userPointsSpan) {
                            userPointsSpan.textContent = data.user_points;
                            currentUser.points = data.user_points;
                        }

                        // 显示代理日志信息
                        if (data.proxy_logs && Array.isArray(data.proxy_logs)) {
                            data.proxy_logs.forEach(proxy_log => {
                                log(proxy_log);
                            });
                        }

                        loadingIndicator.style.display = 'none';

                        if (modelType === 'video') {
                            // 显示视频
                            previewVideo.src = data.video_url;
                            previewVideo.style.display = 'block';

                            // 保存原始URL
                            currentImageUrl = data.video_url;
                            originalImageUrl = data.video_url;

                            // 启用按钮
                            openUrlBtn.disabled = false;
                            downloadBtn.disabled = false;
                            downloadBtn.href = data.video_url;

                            // 更新下载按钮文件名
                            downloadBtn.download = `miaomiao_${data.video_id}.mp4`;

                            log("成功获取视频，可以保存或在浏览器中查看原视频");
                        } else {
                            // 显示图片
                            previewImage.src = data.image_url;
                            previewImage.style.display = 'block';

                            // 保存原始URL
                            currentImageUrl = data.image_url;
                            originalImageUrl = data.original_url || data.image_url;

                            // 启用按钮
                            openUrlBtn.disabled = false;
                            downloadBtn.disabled = false;
                            downloadBtn.href = data.image_url;

                            // 更新下载按钮文件名
                            downloadBtn.download = `miaomiao_${data.image_id}.png`;

                            log("成功获取图片，可以保存或在浏览器中查看原图");
                        }
                    } else {
                        // 检查是否是队列繁忙
                        if (data.queue_busy && modelType === 'video') {
                            const modelName = data.model_name || selectedOption.textContent;
                            log(`队列繁忙: ${data.message}`);
                            log(`模型 ${modelName} 正在被其他用户使用，将在10秒后自动重试...`);

                            // 显示代理日志信息
                            if (data.proxy_logs && Array.isArray(data.proxy_logs)) {
                                data.proxy_logs.forEach(proxy_log => {
                                    log(proxy_log);
                                });
                            }

                            // 10秒后自动重试
                            setTimeout(() => {
                                log(`正在重新尝试生成视频，模型: ${modelName}...`);
                                generateBtn.click();
                            }, 10000);

                            return;
                        }

                        log(`生成失败: ${data.message}`);

                        // 显示代理日志信息
                        if (data.proxy_logs && Array.isArray(data.proxy_logs)) {
                            data.proxy_logs.forEach(proxy_log => {
                                log(proxy_log);
                            });
                        }

                        loadingIndicator.style.display = 'none';
                        previewPlaceholder.style.display = 'block';
                        previewPlaceholder.textContent = "生成失败";
                    }
                })
                .catch(error => {
                    log(`请求过程中发生错误: ${error}`);
                    loadingIndicator.style.display = 'none';
                    previewPlaceholder.style.display = 'block';
                    previewPlaceholder.textContent = "生成失败";
                })
                .finally(() => {
                    // 重新启用生成按钮
                    generateBtn.disabled = false;
                });
            });

            // 在浏览器中打开原图
            openUrlBtn.addEventListener('click', function() {
                if (originalImageUrl) {
                    window.open(originalImageUrl, '_blank');
                }
            });

            // 随机种子复选框
            randomSeedCheck.addEventListener('change', function() {
                if (randomSeedCheck.checked) {
                    seedInput.value = 0;
                }
            });

            // 初始模型检查
            const initialSelectedOption = modelSelect.options[modelSelect.selectedIndex];
            const initialHasProxyUrl = initialSelectedOption.dataset.proxy !== '';
            const initialModelType = initialSelectedOption.dataset.type;

            // 初始化界面状态
            if (initialModelType === 'video') {
                videoParams.style.display = 'block';
                previewTitle.textContent = '视频预览';
                generateBtn.textContent = '生成视频';

                // 初始化时也检查视频模型队列状态
                const initialModelIndex = modelSelect.value;
                log(`🔍 正在检查初始模型 "${initialSelectedOption.textContent}" 的队列状态...`);
                checkVideoModelQueueStatus(initialModelIndex);
            } else {
                videoParams.style.display = 'none';
                previewTitle.textContent = '图片预览';
                generateBtn.textContent = '生成图像';
            }

            // 统一使用管理员配置的代理池，无需用户选择

            // 本地上传功能
            if (uploadImageBtn && imageFileInput) {
                uploadImageBtn.addEventListener('click', function() {
                    imageFileInput.click();
                });

                imageFileInput.addEventListener('change', function() {
                    const file = this.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            if (imagePreview) {
                                imagePreview.src = e.target.result;
                                imagePreview.style.display = 'block';
                            }
                            if (imageStatusText) {
                                imageStatusText.textContent = '图片已上传，正在获取直连URL...';
                            }
                            if (directUrlResult) {
                                directUrlResult.style.display = 'none';
                            }

                            // 自动上传到图床获取直连URL
                            autoUploadImageToHost(e.target.result)
                                .then(url => {
                                    if (imageStatusText) {
                                        imageStatusText.textContent = '图片已上传并获取直连URL';
                                    }
                                })
                                .catch(error => {
                                    if (imageStatusText) {
                                        imageStatusText.textContent = '图片已上传，但获取直连URL失败';
                                    }
                                });
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // 粘贴图片功能
            if (pasteImageBtn) {
                pasteImageBtn.addEventListener('click', function() {
                    navigator.clipboard.read()
                        .then(clipboardItems => {
                            for (const clipboardItem of clipboardItems) {
                                if (clipboardItem.types.includes('image/png') ||
                                    clipboardItem.types.includes('image/jpeg') ||
                                    clipboardItem.types.includes('image/jpg') ||
                                    clipboardItem.types.includes('image/gif')) {

                                    const imageType = clipboardItem.types.find(type =>
                                        type === 'image/png' || type === 'image/jpeg' ||
                                        type === 'image/jpg' || type === 'image/gif'
                                    );

                                    clipboardItem.getType(imageType)
                                        .then(blob => {
                                            const reader = new FileReader();
                                            reader.onload = function(e) {
                                                if (imagePreview) {
                                                    imagePreview.src = e.target.result;
                                                    imagePreview.style.display = 'block';
                                                }
                                                if (imageStatusText) {
                                                    imageStatusText.textContent = '已从剪贴板粘贴图片，正在获取直连URL...';
                                                }
                                                if (directUrlResult) {
                                                    directUrlResult.style.display = 'none';
                                                }

                                                // 自动上传到图床获取直连URL
                                                autoUploadImageToHost(e.target.result)
                                                    .then(url => {
                                                        if (imageStatusText) {
                                                            imageStatusText.textContent = '已从剪贴板粘贴图片并获取直连URL';
                                                        }
                                                    })
                                                    .catch(error => {
                                                        if (imageStatusText) {
                                                            imageStatusText.textContent = '已从剪贴板粘贴图片，但获取直连URL失败';
                                                        }
                                                    });
                                            };
                                            reader.readAsDataURL(blob);
                                        });
                                }
                            }
                        })
                        .catch(err => {
                            console.error('无法访问剪贴板:', err);
                            if (imageStatusText) {
                                imageStatusText.textContent = '粘贴失败，请检查浏览器权限';
                            }
                        });
                });
            }

            // 也支持全局粘贴事件
            document.addEventListener('paste', function(e) {
                // 只有当焦点在图片相关区域时才处理粘贴事件
                const activeElement = document.activeElement;
                if ((imagePathInput && activeElement === imagePathInput) ||
                    (uploadImageBtn && activeElement === uploadImageBtn) ||
                    (pasteImageBtn && activeElement === pasteImageBtn)) {

                    const clipboardData = e.clipboardData;
                    if (clipboardData.items) {
                        for (let i = 0; i < clipboardData.items.length; i++) {
                            if (clipboardData.items[i].type.indexOf('image') !== -1) {
                                const blob = clipboardData.items[i].getAsFile();
                                const reader = new FileReader();
                                reader.onload = function(e) {
                                    if (imagePreview) {
                                        imagePreview.src = e.target.result;
                                        imagePreview.style.display = 'block';
                                    }
                                    if (imageStatusText) {
                                        imageStatusText.textContent = '已从剪贴板粘贴图片，正在获取直连URL...';
                                    }
                                    if (directUrlResult) {
                                        directUrlResult.style.display = 'none';
                                    }

                                    // 自动上传到图床获取直连URL
                                    autoUploadImageToHost(e.target.result)
                                        .then(url => {
                                            if (imageStatusText) {
                                                imageStatusText.textContent = '已从剪贴板粘贴图片并获取直连URL';
                                            }
                                        })
                                        .catch(error => {
                                            if (imageStatusText) {
                                                imageStatusText.textContent = '已从剪贴板粘贴图片，但获取直连URL失败';
                                            }
                                        });
                                };
                                reader.readAsDataURL(blob);
                                e.preventDefault();
                                break;
                            }
                        }
                    }
                }
            });

            // 更新视频生成表单提交
            const videoForm = document.getElementById('videoForm');
            if (videoForm) {
                videoForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                const formData = new FormData();

                // 优先使用直连URL，如果没有URL则使用base64数据
                let imageSource = '';
                if (imagePathInput && imagePathInput.value.trim() !== '') {
                    imageSource = imagePathInput.value.trim(); // 优先使用URL
                } else if (imagePreview && imagePreview.style.display !== 'none') {
                    imageSource = imagePreview.src; // 备用：使用预览图片的base64数据
                } else {
                    alert('请提供图片URL或上传图片');
                    return;
                }

                formData.append('image_source', imageSource);
                formData.append('prompt', document.getElementById('videoPromptInput').value);
                formData.append('width', document.getElementById('videoWidthInput').value);
                formData.append('height', document.getElementById('videoHeightInput').value);
                formData.append('negative_prompt', document.getElementById('videoNegativePromptInput').value);
                formData.append('motion_bucket_id', document.getElementById('motionBucketInput').value);
                formData.append('cond_aug', document.getElementById('condAugInput').value);
                formData.append('steps', document.getElementById('videoStepsInput').value);
                formData.append('seed', document.getElementById('videoSeedInput').value);
                // 统一使用管理员配置的代理池，无需传递use_proxy_address参数
                formData.append('model_index', modelSelect.value);  // 添加模型索引

                // 显示加载状态
                document.getElementById('videoGenerateBtn').disabled = true;
                document.getElementById('videoGenerateBtn').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成中...';

                // 清空之前的日志
                document.getElementById('videoProxyLogs').innerHTML = '';

                // 发送请求
                fetch('/generate_video', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('videoGenerateBtn').disabled = false;
                    document.getElementById('videoGenerateBtn').textContent = '生成视频';

                    if (data.success) {
                        // 显示结果
                        document.getElementById('videoResult').innerHTML = `
                            <div class="alert alert-success">生成成功！</div>
                            <div class="mb-3">
                                <video controls class="img-fluid" style="max-width: 100%;">
                                    <source src="${data.video_url}" type="video/mp4">
                                    您的浏览器不支持视频标签。
                                </video>
                            </div>
                            <div class="mb-3">
                                <a href="${data.video_url}" class="btn btn-primary" target="_blank">在新窗口打开</a>
                                <a href="${data.video_url}" class="btn btn-secondary" download>下载视频</a>
                            </div>
                        `;
                    } else {
                        // 检查是否是队列繁忙
                        if (data.queue_busy) {
                            const modelName = data.model_name || '当前选择的模型';
                            document.getElementById('videoResult').innerHTML = `
                                <div class="alert alert-warning">队列繁忙：${data.message}</div>
                                <div class="alert alert-info">模型 ${modelName} 正在被其他用户使用，将在10秒后自动重试...</div>
                            `;

                            // 10秒后自动重试
                            setTimeout(() => {
                                document.getElementById('videoResult').innerHTML = `
                                    <div class="alert alert-info">正在重新尝试生成视频，模型: ${modelName}...</div>
                                `;
                                // 重新提交表单
                                videoForm.dispatchEvent(new Event('submit'));
                            }, 10000);

                            return;
                        }

                        document.getElementById('videoResult').innerHTML = `
                            <div class="alert alert-danger">生成失败：${data.message}</div>
                        `;
                    }

                    // 显示代理日志
                    if (data.proxy_logs && data.proxy_logs.length > 0) {
                        const logsHtml = data.proxy_logs.map(log => `<div>${log}</div>`).join('');
                        document.getElementById('videoProxyLogs').innerHTML = logsHtml;
                    }
                })
                .catch(error => {
                    document.getElementById('videoGenerateBtn').disabled = false;
                    document.getElementById('videoGenerateBtn').textContent = '生成视频';
                    document.getElementById('videoResult').innerHTML = `
                        <div class="alert alert-danger">请求错误：${error}</div>
                    `;
                });
                });
            }



            // 复制直连URL
            if (copyDirectUrlBtn) {
                copyDirectUrlBtn.addEventListener('click', function() {
                    if (directUrlText) {
                        directUrlText.select();
                        document.execCommand('copy');
                        log('直连URL已复制到剪贴板');
                    }
                });
            }

            // 使用直连URL
            if (useDirectUrlBtn) {
                useDirectUrlBtn.addEventListener('click', function() {
                    if (directUrlText && imagePathInput) {
                        imagePathInput.value = directUrlText.value;
                        if (imagePreview) {
                            imagePreview.style.display = 'none';
                        }
                        if (directUrlResult) {
                            directUrlResult.style.display = 'none';
                        }
                        if (imageStatusText) {
                            imageStatusText.textContent = '已使用直连URL';
                        }
                        log('已将直连URL设置为图片源');
                    }
                });
            }

            // 管理员面板
            if (adminBtn) {
                adminBtn.addEventListener('click', function() {
                    adminModal.show();
                    loadAdminData();
                });
            }

            // ==================== 兑换码管理相关函数 ====================

            // 显示兑换码统计
            window.displayRedemptionStats = function(stats) {
                // 更新统计卡片
                document.getElementById('totalCodesCount').textContent = stats.total_codes || 0;
                document.getElementById('activeCodesCount').textContent = stats.active_codes || 0;
                document.getElementById('usedCodesCount').textContent = stats.total_codes_used || 0;
                document.getElementById('totalPointsDistributed').textContent = stats.total_points_distributed || 0;
            };

            // 显示最近使用记录
            window.displayRecentRedemptions = function(records) {
                const container = document.getElementById('recentRedemptionsContainer');

                if (records.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无使用记录</p></div>';
                    return;
                }

                let html = '';
                records.slice(0, 20).forEach((record, index) => {
                    const date = new Date(record.used_at).toLocaleString();
                    const typeText = record.code_type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = record.code_type === 'one_time' ? 'bg-primary' : 'bg-success';

                    html += `
                        <div class="d-flex align-items-center p-3 ${index < records.length - 1 ? 'border-bottom' : ''}">
                            <div class="flex-shrink-0">
                                <div class="bg-light rounded-circle p-2">
                                    <i class="fas fa-user text-muted"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${record.username}</h6>
                                        <p class="mb-1 text-muted small">兑换码: <code>${record.code}</code></p>
                                        <small class="text-muted">${date}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge ${typeClass} mb-1">${typeText}</span>
                                        <div class="text-success fw-bold">+${record.points} 积分</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            };

            // 显示兑换码列表
            window.displayRedemptionCodesTable = function(codes) {
                const container = document.getElementById('redemptionCodesContainer');
                const countBadge = document.getElementById('codesCountBadge');

                // 更新数量徽章
                countBadge.textContent = codes.length;

                if (codes.length === 0) {
                    container.innerHTML = '<div class="text-center p-4"><p class="text-muted mb-0">暂无兑换码</p></div>';
                    return;
                }

                let html = '';
                codes.forEach((code, index) => {
                    const expireDate = new Date(code.expire_at);
                    const isExpired = new Date() > expireDate;
                    const createDate = new Date(code.created_at);

                    // 状态判断
                    let statusText, statusClass, statusIcon;
                    if (isExpired) {
                        statusText = '已过期';
                        statusClass = 'bg-danger';
                        statusIcon = 'fas fa-times-circle';
                    } else if (!code.is_active) {
                        statusText = '已禁用';
                        statusClass = 'bg-warning';
                        statusIcon = 'fas fa-pause-circle';
                    } else if (code.type === 'one_time' && code.used_count > 0) {
                        statusText = '已使用';
                        statusClass = 'bg-secondary';
                        statusIcon = 'fas fa-check-circle';
                    } else {
                        statusText = '有效';
                        statusClass = 'bg-success';
                        statusIcon = 'fas fa-check-circle';
                    }

                    const typeText = code.type === 'one_time' ? '一次性' : '活动码';
                    const typeClass = code.type === 'one_time' ? 'bg-primary' : 'bg-info';

                    html += `
                        <div class="d-flex align-items-center p-3 ${index < codes.length - 1 ? 'border-bottom' : ''}">
                            <div class="form-check me-3">
                                <input class="form-check-input code-checkbox" type="checkbox" value="${code.code}" id="code_${code.code}">
                            </div>
                            <div class="flex-grow-1">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-ticket-alt text-primary fs-5"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1"><code class="text-dark">${code.code}</code></h6>
                                                <small class="text-muted">创建于 ${createDate.toLocaleDateString()}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge ${typeClass}">${typeText}</span>
                                        <div class="small text-muted mt-1">${code.points} 积分</div>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge ${statusClass}">
                                            <i class="${statusIcon} me-1"></i>${statusText}
                                        </span>
                                        <div class="small text-muted mt-1">使用 ${code.used_count} 次</div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="small">
                                            <div class="text-muted">过期时间</div>
                                            <div class="${isExpired ? 'text-danger' : 'text-dark'}">${expireDate.toLocaleDateString()}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="small text-muted" title="${code.description || '无描述'}">
                                            ${code.description ? (code.description.length > 20 ? code.description.substring(0, 20) + '...' : code.description) : '无描述'}
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                ${!isExpired ? `
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="toggleRedemptionCode('${code.code}', '${code.is_active ? 'deactivate' : 'activate'}')">
                                                            <i class="fas fa-${code.is_active ? 'pause' : 'play'} me-2"></i>
                                                            ${code.is_active ? '禁用' : '启用'}
                                                        </a>
                                                    </li>
                                                ` : ''}
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="copyCodeToClipboard('${code.code}')">
                                                        <i class="fas fa-copy me-2"></i>复制兑换码
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteRedemptionCode('${code.code}')">
                                                        <i class="fas fa-trash me-2"></i>删除
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            };

            // 加载兑换码数据
            window.loadRedemptionData = function() {
                // 加载兑换码统计
                fetch('/admin/redemption_statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionStats(data.statistics);
                        } else {
                            console.error('加载统计数据失败:', data.message);
                            // 显示错误状态
                            document.getElementById('totalCodesCount').textContent = '-';
                            document.getElementById('activeCodesCount').textContent = '-';
                            document.getElementById('usedCodesCount').textContent = '-';
                            document.getElementById('totalPointsDistributed').textContent = '-';
                        }
                    })
                    .catch(error => {
                        console.error('加载统计数据失败:', error);
                        // 显示错误状态
                        document.getElementById('totalCodesCount').textContent = '-';
                        document.getElementById('activeCodesCount').textContent = '-';
                        document.getElementById('usedCodesCount').textContent = '-';
                        document.getElementById('totalPointsDistributed').textContent = '-';
                    });

                // 加载兑换码列表
                fetch('/admin/redemption_codes')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRedemptionCodesTable(data.codes);
                        } else {
                            const container = document.getElementById('redemptionCodesContainer');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('redemptionCodesContainer');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载兑换码失败: ${error}</div>`;
                        }
                    });

                // 加载使用记录
                fetch('/admin/redemption_usage_records')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayRecentRedemptions(data.records);
                        } else {
                            const container = document.getElementById('recentRedemptionsContainer');
                            if (container) {
                                container.innerHTML = `<div class="alert alert-danger m-3">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const container = document.getElementById('recentRedemptionsContainer');
                        if (container) {
                            container.innerHTML = `<div class="alert alert-danger m-3">加载使用记录失败: ${error}</div>`;
                        }
                    });
            };

            // 加载管理员数据
            function loadAdminData() {
                // 加载用户列表
                fetch('/admin/users')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayUsersTable(data.users);
                        } else {
                            document.getElementById('usersTable').innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                        }
                    })
                    .catch(error => {
                        document.getElementById('usersTable').innerHTML = `<div class="alert alert-danger">加载用户数据失败: ${error}</div>`;
                    });

                // 加载统计信息
                fetch('/admin/statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayStatistics(data.statistics);
                        } else {
                            document.getElementById('statisticsContent').innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                        }
                    })
                    .catch(error => {
                        document.getElementById('statisticsContent').innerHTML = `<div class="alert alert-danger">加载统计数据失败: ${error}</div>`;
                    });

                // 加载待审核用户
                fetch('/admin/pending_users')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayPendingUsersTable(data.pending_users);
                        } else {
                            document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                        }
                    })
                    .catch(error => {
                        document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">加载待审核用户失败: ${error}</div>`;
                    });

                // 加载系统设置
                fetch('/admin/settings')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('globalProxyApiUrl').value = data.settings.global_proxy_api_url || '';
                            document.getElementById('imageGenerationCost').value = data.settings.image_generation_cost || 1;
                            document.getElementById('videoGenerationCost').value = data.settings.video_generation_cost || 5;
                            document.getElementById('requireRegistrationApproval').checked = data.settings.require_registration_approval || false;
                        }
                    })
                    .catch(error => {
                        console.error('加载系统设置失败:', error);
                    });

                // 加载兑换码数据
                loadRedemptionData();
            }

            // 显示用户表格
            function displayUsersTable(users) {
                let html = `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>积分</th>
                                    <th>生成次数</th>
                                    <th>注册时间</th>
                                    <th>最后登录</th>
                                    <th>管理员</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                users.forEach(user => {
                    html += `
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.email || '-'}</td>
                            <td>${user.points}</td>
                            <td>${user.total_generated}</td>
                            <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}</td>
                            <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : '-'}</td>
                            <td>${user.is_admin ? '是' : '否'}</td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                document.getElementById('usersTable').innerHTML = html;
            }

            // 显示待审核用户表格
            function displayPendingUsersTable(pendingUsers) {
                if (pendingUsers.length === 0) {
                    document.getElementById('pendingUsersTable').innerHTML = '<div class="alert alert-info">暂无待审核用户</div>';
                    return;
                }

                let html = `
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                pendingUsers.forEach(user => {
                    html += `
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.email || '-'}</td>
                            <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}</td>
                            <td>
                                <button class="btn btn-success btn-sm me-1" onclick="approveUser('${user.username}')">通过</button>
                                <button class="btn btn-danger btn-sm" onclick="rejectUser('${user.username}')">拒绝</button>
                            </td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                document.getElementById('pendingUsersTable').innerHTML = html;
            }

            // 显示统计信息
            function displayStatistics(stats) {
                const html = `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">系统统计</h6>
                                    <p>总用户数: ${stats.total_users}</p>
                                    <p>总交易数: ${stats.system_stats.total_transactions}</p>
                                    <p>总发放积分: ${stats.system_stats.total_points_issued}</p>
                                    <p>总消耗积分: ${stats.system_stats.total_points_consumed}</p>
                                    <p>总生成次数: ${stats.system_stats.total_generations}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">生成排行榜</h6>
                                    ${stats.top_users.map((user, index) =>
                                        `<p>${index + 1}. ${user.username}: ${user.generations}次</p>`
                                    ).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>最近活动</h6>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>用户</th>
                                        <th>积分变化</th>
                                        <th>类型</th>
                                        <th>描述</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${stats.recent_activities.map(activity => `
                                        <tr>
                                            <td>${activity.username}</td>
                                            <td class="${activity.points_change > 0 ? 'text-success' : 'text-danger'}">
                                                ${activity.points_change > 0 ? '+' : ''}${activity.points_change}
                                            </td>
                                            <td>${activity.type}</td>
                                            <td>${activity.description}</td>
                                            <td>${new Date(activity.timestamp).toLocaleString()}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                document.getElementById('statisticsContent').innerHTML = html;
            }

            // 积分充值功能
            const chargeBtn = document.getElementById('chargeBtn');
            if (chargeBtn) {
                chargeBtn.addEventListener('click', function() {
                    const username = document.getElementById('chargeUsername').value.trim();
                    const points = parseInt(document.getElementById('chargePoints').value);
                    const reason = document.getElementById('chargeReason').value.trim() || '管理员充值';

                    if (!username || !points || points <= 0) {
                        alert('请填写正确的用户名和积分数');
                        return;
                    }

                    fetch('/admin/add_points', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            points: points,
                            reason: reason
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message);
                            // 清空表单
                            document.getElementById('chargeUsername').value = '';
                            document.getElementById('chargePoints').value = '';
                            document.getElementById('chargeReason').value = '';
                            // 重新加载数据
                            loadAdminData();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`充值失败: ${error}`);
                    });
                });
            }

            // 保存代理池设置
            const saveProxySettingsBtn = document.getElementById('saveProxySettingsBtn');
            if (saveProxySettingsBtn) {
                saveProxySettingsBtn.addEventListener('click', function() {
                    const globalProxyApiUrl = document.getElementById('globalProxyApiUrl').value.trim();

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            global_proxy_api_url: globalProxyApiUrl
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('代理池设置保存成功！');
                            // 刷新页面以更新全局代理池状态
                            location.reload();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`保存失败: ${error}`);
                    });
                });
            }

            // 保存积分设置
            const savePointsSettingsBtn = document.getElementById('savePointsSettingsBtn');
            if (savePointsSettingsBtn) {
                savePointsSettingsBtn.addEventListener('click', function() {
                    const imageGenerationCost = parseInt(document.getElementById('imageGenerationCost').value);
                    const videoGenerationCost = parseInt(document.getElementById('videoGenerationCost').value);

                    if (imageGenerationCost < 1 || videoGenerationCost < 1) {
                        alert('积分消耗必须大于0');
                        return;
                    }

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            image_generation_cost: imageGenerationCost,
                            video_generation_cost: videoGenerationCost
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('积分设置保存成功！');
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`保存失败: ${error}`);
                    });
                });
            }

            // 保存注册设置
            const saveRegistrationSettingsBtn = document.getElementById('saveRegistrationSettingsBtn');
            if (saveRegistrationSettingsBtn) {
                saveRegistrationSettingsBtn.addEventListener('click', function() {
                    const requireRegistrationApproval = document.getElementById('requireRegistrationApproval').checked;

                    fetch('/admin/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            require_registration_approval: requireRegistrationApproval
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('注册设置保存成功！');
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        alert(`保存失败: ${error}`);
                    });
                });
            }

            // 刷新待审核用户按钮
            const refreshPendingUsersBtn = document.getElementById('refreshPendingUsersBtn');
            if (refreshPendingUsersBtn) {
                refreshPendingUsersBtn.addEventListener('click', function() {
                    fetch('/admin/pending_users')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                displayPendingUsersTable(data.pending_users);
                            } else {
                                document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                            }
                        })
                        .catch(error => {
                            document.getElementById('pendingUsersTable').innerHTML = `<div class="alert alert-danger">加载待审核用户失败: ${error}</div>`;
                        });
                });
            }
        });

        // 审核通过用户
        function approveUser(username) {
            if (confirm(`确定要通过用户 "${username}" 的审核吗？`)) {
                fetch('/admin/approve_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        // 刷新待审核用户列表
                        document.getElementById('refreshPendingUsersBtn').click();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`操作失败: ${error}`);
                });
            }
        }

        // 拒绝用户
        function rejectUser(username) {
            const reason = prompt(`请输入拒绝用户 "${username}" 的原因（可选）:`);
            if (reason !== null) { // 用户没有取消
                fetch('/admin/reject_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        reason: reason
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        // 刷新待审核用户列表
                        document.getElementById('refreshPendingUsersBtn').click();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`操作失败: ${error}`);
                });
            }





            // 复制兑换码到剪贴板
            window.copyCodeToClipboard = function(code) {
                navigator.clipboard.writeText(code).then(function() {
                    // 显示成功提示
                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
                    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fas fa-check me-2"></i>兑换码已复制到剪贴板
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.show();
                    toast.addEventListener('hidden.bs.toast', () => {
                        document.body.removeChild(toast);
                    });
                }).catch(function() {
                    alert('复制失败，请手动复制：' + code);
                });
            };

            // 应用兑换码模板
            window.applyCodeTemplate = function(templateType) {
                const templates = {
                    welcome: { type: 'one_time', points: 10, count: 1, days: 30, desc: '新用户欢迎奖励' },
                    activity: { type: 'activity', points: 50, count: 1, days: 7, desc: '活动奖励' },
                    vip: { type: 'activity', points: 100, count: 1, days: 60, desc: 'VIP用户福利' },
                    test: { type: 'one_time', points: 1, count: 1, days: 1, desc: '测试用兑换码' }
                };

                const template = templates[templateType];
                if (template) {
                    document.getElementById('newCodeType').value = template.type;
                    document.getElementById('newCodePoints').value = template.points;
                    document.getElementById('newCodeCount').value = template.count;
                    document.getElementById('newCodeExpireDays').value = template.days;
                    document.getElementById('newCodeDescription').value = template.desc;
                    updateCreateCodePreview();
                }
            };

            // 更新创建兑换码预览
            window.updateCreateCodePreview = function() {
                const type = document.getElementById('newCodeType').value;
                const points = parseInt(document.getElementById('newCodePoints').value) || 0;
                const count = parseInt(document.getElementById('newCodeCount').value) || 0;
                const days = parseInt(document.getElementById('newCodeExpireDays').value) || 0;

                const typeText = type === 'one_time' ? '一次性' : '活动';
                const expireDate = new Date();
                expireDate.setDate(expireDate.getDate() + days);

                document.getElementById('previewType').textContent = typeText;
                document.getElementById('previewCount').textContent = count;
                document.getElementById('previewPoints').textContent = points;
                document.getElementById('previewExpireDate').textContent = expireDate.toLocaleDateString();
                document.getElementById('previewTotalPoints').textContent = points * count;
            };

            // ==================== 新的兑换码管理事件监听器 ====================

            // 创建兑换码按钮
            const createCodeBtn = document.getElementById('createCodeBtn');
            const createCodeModal = new bootstrap.Modal(document.getElementById('createCodeModal'));
            if (createCodeBtn) {
                createCodeBtn.addEventListener('click', function() {
                    createCodeModal.show();
                    updateCreateCodePreview();
                });
            }

            // 创建兑换码表单输入变化时更新预览
            ['newCodeType', 'newCodePoints', 'newCodeCount', 'newCodeExpireDays'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updateCreateCodePreview);
                    element.addEventListener('change', updateCreateCodePreview);
                }
            });

            // 确认创建兑换码
            const confirmCreateCodeBtn = document.getElementById('confirmCreateCodeBtn');
            if (confirmCreateCodeBtn) {
                confirmCreateCodeBtn.addEventListener('click', function() {
                    const codeType = document.getElementById('newCodeType').value;
                    const points = parseInt(document.getElementById('newCodePoints').value);
                    const count = parseInt(document.getElementById('newCodeCount').value);
                    const expireDays = parseInt(document.getElementById('newCodeExpireDays').value);
                    const description = document.getElementById('newCodeDescription').value.trim();

                    if (!points || points <= 0) {
                        alert('请输入有效的积分数');
                        return;
                    }

                    if (!count || count <= 0 || count > 1000) {
                        alert('生成数量必须在1-1000之间');
                        return;
                    }

                    if (!expireDays || expireDays <= 0 || expireDays > 365) {
                        alert('有效天数必须在1-365之间');
                        return;
                    }

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: codeType,
                            points: points,
                            count: count,
                            expire_days: expireDays,
                            description: description
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-magic me-1"></i>生成兑换码';

                        if (data.success) {
                            // 显示成功消息和生成的兑换码
                            const codesText = data.codes.join('\n');
                            const message = `${data.message}\n\n生成的兑换码:\n${codesText}`;

                            // 创建一个更好的显示对话框
                            const modalHtml = `
                                <div class="modal fade" id="codesResultModal" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header bg-success text-white">
                                                <h5 class="modal-title">
                                                    <i class="fas fa-check-circle me-2"></i>兑换码生成成功
                                                </h5>
                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p class="mb-3">${data.message}</p>
                                                <div class="alert alert-info">
                                                    <h6 class="alert-heading">生成的兑换码：</h6>
                                                    <textarea class="form-control" rows="6" readonly>${codesText}</textarea>
                                                    <div class="mt-2">
                                                        <button class="btn btn-outline-primary btn-sm" onclick="navigator.clipboard.writeText('${codesText}')">
                                                            <i class="fas fa-copy me-1"></i>复制全部
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;

                            // 添加模态框到页面
                            document.body.insertAdjacentHTML('beforeend', modalHtml);
                            const resultModal = new bootstrap.Modal(document.getElementById('codesResultModal'));
                            resultModal.show();

                            // 模态框关闭后移除
                            document.getElementById('codesResultModal').addEventListener('hidden.bs.modal', function() {
                                this.remove();
                            });

                            // 关闭创建模态框
                            createCodeModal.hide();

                            // 清空表单
                            document.getElementById('newCodeType').value = 'one_time';
                            document.getElementById('newCodePoints').value = '10';
                            document.getElementById('newCodeCount').value = '1';
                            document.getElementById('newCodeExpireDays').value = '30';
                            document.getElementById('newCodeDescription').value = '';
                            updateCreateCodePreview();

                            // 重新加载数据
                            loadRedemptionData();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-magic me-1"></i>生成兑换码';
                        alert(`生成失败: ${error}`);
                    });
                });
            }

            // 筛选和搜索功能
            const applyFiltersBtn = document.getElementById('applyFiltersBtn');
            const clearFiltersBtn = document.getElementById('clearFiltersBtn');

            if (applyFiltersBtn) {
                applyFiltersBtn.addEventListener('click', function() {
                    // 这里可以添加筛选逻辑
                    loadRedemptionData();
                });
            }

            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    document.getElementById('searchCodeInput').value = '';
                    document.getElementById('filterCodeType').value = '';
                    document.getElementById('filterCodeStatus').value = '';
                    document.getElementById('filterPointsRange').value = '';
                    loadRedemptionData();
                });
            }

            // 全选/取消全选
            const selectAllCodesBtn = document.getElementById('selectAllCodesBtn');
            const deselectAllCodesBtn = document.getElementById('deselectAllCodesBtn');

            if (selectAllCodesBtn) {
                selectAllCodesBtn.addEventListener('click', function() {
                    document.querySelectorAll('.code-checkbox').forEach(cb => cb.checked = true);
                    updateSelectedCodesCount();
                });
            }

            if (deselectAllCodesBtn) {
                deselectAllCodesBtn.addEventListener('click', function() {
                    document.querySelectorAll('.code-checkbox').forEach(cb => cb.checked = false);
                    updateSelectedCodesCount();
                });
            }

            // 更新选中数量
            window.updateSelectedCodesCount = function() {
                const selectedCount = document.querySelectorAll('.code-checkbox:checked').length;
                const countElement = document.getElementById('selectedCodesCount');
                if (countElement) {
                    countElement.textContent = selectedCount;
                }
            };

            // 刷新按钮
            const refreshCodesBtn = document.getElementById('refreshCodesBtn');
            if (refreshCodesBtn) {
                refreshCodesBtn.addEventListener('click', function() {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
                    loadRedemptionData();
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新';
                    }, 1000);
                });
            }

            // 批量操作功能
            const batchOperationBtn = document.getElementById('batchOperationBtn');
            const batchOperationModal = new bootstrap.Modal(document.getElementById('batchOperationModal'));

            if (batchOperationBtn) {
                batchOperationBtn.addEventListener('click', function() {
                    const selectedCodes = document.querySelectorAll('.code-checkbox:checked');
                    if (selectedCodes.length === 0) {
                        alert('请先选择要操作的兑换码');
                        return;
                    }
                    updateSelectedCodesCount();
                    batchOperationModal.show();
                });
            }

            // 批量启用
            const batchActivateBtn = document.getElementById('batchActivateBtn');
            if (batchActivateBtn) {
                batchActivateBtn.addEventListener('click', function() {
                    const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked')).map(cb => cb.value);
                    if (selectedCodes.length === 0) return;

                    if (!confirm(`确定要启用选中的 ${selectedCodes.length} 个兑换码吗？`)) return;

                    batchOperationCodes(selectedCodes, 'activate');
                });
            }

            // 批量禁用
            const batchDeactivateBtn = document.getElementById('batchDeactivateBtn');
            if (batchDeactivateBtn) {
                batchDeactivateBtn.addEventListener('click', function() {
                    const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked')).map(cb => cb.value);
                    if (selectedCodes.length === 0) return;

                    if (!confirm(`确定要禁用选中的 ${selectedCodes.length} 个兑换码吗？`)) return;

                    batchOperationCodes(selectedCodes, 'deactivate');
                });
            }

            // 批量删除
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            if (batchDeleteBtn) {
                batchDeleteBtn.addEventListener('click', function() {
                    const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked')).map(cb => cb.value);
                    if (selectedCodes.length === 0) return;

                    if (!confirm(`确定要删除选中的 ${selectedCodes.length} 个兑换码吗？此操作不可恢复！`)) return;

                    batchOperationCodes(selectedCodes, 'delete');
                });
            }

            // 批量操作函数
            window.batchOperationCodes = function(codes, action) {
                const promises = codes.map(code => {
                    let url, method, body;

                    if (action === 'delete') {
                        url = '/admin/delete_redemption_code';
                        method = 'POST';
                        body = JSON.stringify({ code: code });
                    } else {
                        url = '/admin/toggle_redemption_code';
                        method = 'POST';
                        body = JSON.stringify({ code: code, action: action });
                    }

                    return fetch(url, {
                        method: method,
                        headers: { 'Content-Type': 'application/json' },
                        body: body
                    }).then(response => response.json());
                });

                Promise.all(promises).then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;

                    let message = `操作完成！成功: ${successCount}`;
                    if (failCount > 0) {
                        message += `，失败: ${failCount}`;
                    }

                    alert(message);
                    batchOperationModal.hide();
                    loadRedemptionData();
                }).catch(error => {
                    alert(`批量操作失败: ${error}`);
                });
            };

            // 导出功能
            const exportCodesBtn = document.getElementById('exportCodesBtn');
            if (exportCodesBtn) {
                exportCodesBtn.addEventListener('click', function() {
                    // 这里可以添加导出功能
                    alert('导出功能开发中...');
                });
            }

            // 生成兑换码（旧版本兼容）
            if (document.getElementById('generateCodesBtn')) {
                document.getElementById('generateCodesBtn').addEventListener('click', function() {
                    const codeType = document.getElementById('codeType').value;
                    const points = parseInt(document.getElementById('codePoints').value);
                    const count = parseInt(document.getElementById('codeCount').value);
                    const expireDays = parseInt(document.getElementById('codeExpireDays').value);
                    const description = document.getElementById('codeDescription').value.trim();

                    if (!points || points <= 0) {
                        alert('请输入有效的积分数');
                        return;
                    }

                    if (!count || count <= 0 || count > 1000) {
                        alert('生成数量必须在1-1000之间');
                        return;
                    }

                    if (!expireDays || expireDays <= 0 || expireDays > 365) {
                        alert('有效天数必须在1-365之间');
                        return;
                    }

                    this.disabled = true;
                    this.textContent = '生成中...';

                    fetch('/admin/create_redemption_codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: codeType,
                            points: points,
                            count: count,
                            expire_days: expireDays,
                            description: description
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.disabled = false;
                        this.textContent = '生成';

                        if (data.success) {
                            alert(`${data.message}\n\n生成的兑换码:\n${data.codes.join('\n')}`);
                            // 清空表单
                            document.getElementById('codePoints').value = '10';
                            document.getElementById('codeCount').value = '1';
                            document.getElementById('codeExpireDays').value = '30';
                            document.getElementById('codeDescription').value = '';
                            // 重新加载数据
                            loadRedemptionData();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.textContent = '生成';
                        alert(`生成失败: ${error}`);
                    });
                });
            }

            // 刷新兑换码数据
            if (document.getElementById('refreshRedemptionCodesBtn')) {
                document.getElementById('refreshRedemptionCodesBtn').addEventListener('click', function() {
                    loadRedemptionData();
                });
            }

            // 切换兑换码状态
            window.toggleRedemptionCode = function(code, action) {
                if (!confirm(`确定要${action === 'activate' ? '启用' : '禁用'}这个兑换码吗？`)) {
                    return;
                }

                fetch('/admin/toggle_redemption_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        action: action
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadRedemptionData();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`操作失败: ${error}`);
                });
            };

            // 删除兑换码
            window.deleteRedemptionCode = function(code) {
                if (!confirm(`确定要删除兑换码 ${code} 吗？此操作不可恢复！`)) {
                    return;
                }

                fetch('/admin/delete_redemption_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadRedemptionData();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    alert(`删除失败: ${error}`);
                });
            };

            // ==================== 用户兑换码功能 ====================

            // 兑换码功能
            const redeemBtn = document.getElementById('redeemBtn');
            const redemptionCodeInput = document.getElementById('redemptionCodeInput');

            if (redeemBtn && redemptionCodeInput) {
                redeemBtn.addEventListener('click', function() {
                    const code = redemptionCodeInput.value.trim().toUpperCase();

                    if (!code) {
                        alert('请输入兑换码');
                        return;
                    }

                    // 检查登录状态
                    if (!checkLoginStatus()) {
                        return;
                    }

                    this.disabled = true;
                    this.textContent = '兑换中...';

                    fetch('/redeem_code', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            code: code
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.disabled = false;
                        this.textContent = '兑换';

                        if (data.success) {
                            alert(`${data.message}\n当前积分: ${data.new_points}`);
                            // 更新积分显示
                            if (userPointsSpan) {
                                userPointsSpan.textContent = data.new_points;
                            }
                            if (currentUser) {
                                currentUser.points = data.new_points;
                            }
                            // 清空输入框
                            redemptionCodeInput.value = '';
                            log(`兑换成功！获得 ${data.points_gained} 积分，当前积分: ${data.new_points}`);
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.textContent = '兑换';
                        alert(`兑换失败: ${error}`);
                    });
                });

                // 回车键兑换
                redemptionCodeInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        redeemBtn.click();
                    }
                });
            }
        }
    </script>
</body>
</html>
