#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的兑换码管理界面功能
"""

import requests
import json
import time
from auth import UserManager
from redemption_system import RedemptionSystem

def test_redemption_management():
    """测试兑换码管理功能"""
    print("=== 测试新的兑换码管理界面 ===\n")
    
    # 初始化系统
    rs = RedemptionSystem()
    um = UserManager()
    
    # 创建测试管理员用户
    print("1. 创建测试管理员用户...")
    admin_username = 'test_admin'
    admin_password = 'admin123'
    
    # 删除已存在的测试用户
    if admin_username in um.users:
        del um.users[admin_username]
        um.save_users()
    
    # 注册管理员用户
    success, message = um.register_user(admin_username, admin_password, '<EMAIL>')
    if success:
        # 设置为管理员
        um.users[admin_username]['is_admin'] = True
        um.save_users()
        print(f"   ✅ 管理员用户创建成功: {admin_username}")
    else:
        print(f"   ❌ 管理员用户创建失败: {message}")
        return
    
    # 测试API端点
    base_url = 'http://localhost:5000'
    session = requests.Session()
    
    # 登录管理员
    print("\n2. 登录管理员账户...")
    login_data = {
        'username': admin_username,
        'password': admin_password
    }
    
    response = session.post(f'{base_url}/login', json=login_data)
    if response.status_code == 200 and response.json().get('success'):
        print("   ✅ 管理员登录成功")
    else:
        print(f"   ❌ 管理员登录失败: {response.text}")
        return
    
    # 测试获取兑换码统计
    print("\n3. 测试获取兑换码统计...")
    response = session.get(f'{base_url}/admin/redemption_statistics')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            stats = data['statistics']
            print(f"   ✅ 统计获取成功:")
            print(f"      总兑换码数: {stats.get('total_codes', 0)}")
            print(f"      有效兑换码: {stats.get('active_codes', 0)}")
            print(f"      已使用: {stats.get('total_codes_used', 0)}")
            print(f"      总发放积分: {stats.get('total_points_distributed', 0)}")
        else:
            print(f"   ❌ 统计获取失败: {data.get('message')}")
    else:
        print(f"   ❌ 统计API请求失败: {response.status_code}")
    
    # 测试创建兑换码
    print("\n4. 测试创建兑换码...")
    create_data = {
        'type': 'one_time',
        'points': 50,
        'count': 3,
        'expire_days': 30,
        'description': '测试兑换码'
    }
    
    response = session.post(f'{base_url}/admin/create_redemption_codes', json=create_data)
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            codes = data['codes']
            print(f"   ✅ 兑换码创建成功，生成了 {len(codes)} 个兑换码:")
            for i, code in enumerate(codes, 1):
                print(f"      {i}. {code}")
            test_code = codes[0]  # 保存第一个兑换码用于后续测试
        else:
            print(f"   ❌ 兑换码创建失败: {data.get('message')}")
            return
    else:
        print(f"   ❌ 创建兑换码API请求失败: {response.status_code}")
        return
    
    # 测试获取兑换码列表
    print("\n5. 测试获取兑换码列表...")
    response = session.get(f'{base_url}/admin/redemption_codes')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            codes = data['codes']
            print(f"   ✅ 兑换码列表获取成功，共 {len(codes)} 个兑换码")
            if codes:
                print("   最新兑换码信息:")
                latest_code = codes[0]
                print(f"      兑换码: {latest_code['code']}")
                print(f"      类型: {latest_code['type']}")
                print(f"      积分: {latest_code['points']}")
                print(f"      状态: {'有效' if latest_code['is_active'] else '禁用'}")
        else:
            print(f"   ❌ 兑换码列表获取失败: {data.get('message')}")
    else:
        print(f"   ❌ 兑换码列表API请求失败: {response.status_code}")
    
    # 测试兑换码操作
    print("\n6. 测试兑换码操作...")
    
    # 测试禁用兑换码
    toggle_data = {
        'code': test_code,
        'action': 'deactivate'
    }
    
    response = session.post(f'{base_url}/admin/toggle_redemption_code', json=toggle_data)
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"   ✅ 兑换码禁用成功: {data.get('message')}")
        else:
            print(f"   ❌ 兑换码禁用失败: {data.get('message')}")
    else:
        print(f"   ❌ 禁用兑换码API请求失败: {response.status_code}")
    
    # 测试重新启用兑换码
    toggle_data['action'] = 'activate'
    response = session.post(f'{base_url}/admin/toggle_redemption_code', json=toggle_data)
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"   ✅ 兑换码启用成功: {data.get('message')}")
        else:
            print(f"   ❌ 兑换码启用失败: {data.get('message')}")
    else:
        print(f"   ❌ 启用兑换码API请求失败: {response.status_code}")
    
    # 创建普通用户测试兑换功能
    print("\n7. 测试用户兑换功能...")
    test_user = 'test_user'
    test_password = 'user123'
    
    # 删除已存在的测试用户
    if test_user in um.users:
        del um.users[test_user]
        um.save_users()
    
    # 注册普通用户
    success, message = um.register_user(test_user, test_password, '<EMAIL>')
    if success:
        print(f"   ✅ 测试用户创建成功: {test_user}")
        
        # 登录普通用户
        login_data = {
            'username': test_user,
            'password': test_password
        }
        
        user_session = requests.Session()
        response = user_session.post(f'{base_url}/login', json=login_data)
        if response.status_code == 200 and response.json().get('success'):
            print("   ✅ 测试用户登录成功")
            
            # 测试兑换码使用
            redeem_data = {
                'code': test_code
            }
            
            response = user_session.post(f'{base_url}/redeem_code', json=redeem_data)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"   ✅ 兑换码使用成功: {data.get('message')}")
                    print(f"      获得积分: {data.get('points_gained')}")
                    print(f"      当前积分: {data.get('new_points')}")
                else:
                    print(f"   ❌ 兑换码使用失败: {data.get('message')}")
            else:
                print(f"   ❌ 兑换码使用API请求失败: {response.status_code}")
        else:
            print(f"   ❌ 测试用户登录失败: {response.text}")
    else:
        print(f"   ❌ 测试用户创建失败: {message}")
    
    # 测试使用记录获取
    print("\n8. 测试使用记录获取...")
    response = session.get(f'{base_url}/admin/redemption_usage_records')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            records = data['records']
            print(f"   ✅ 使用记录获取成功，共 {len(records)} 条记录")
            if records:
                print("   最新使用记录:")
                latest_record = records[0]
                print(f"      用户: {latest_record['username']}")
                print(f"      兑换码: {latest_record['code']}")
                print(f"      积分: {latest_record['points']}")
                print(f"      时间: {latest_record['used_at']}")
        else:
            print(f"   ❌ 使用记录获取失败: {data.get('message')}")
    else:
        print(f"   ❌ 使用记录API请求失败: {response.status_code}")
    
    print("\n=== 测试完成 ===")
    print("✅ 新的兑换码管理界面功能测试通过！")
    print("\n主要改进:")
    print("1. 🎨 现代化的卡片式界面设计")
    print("2. 📊 直观的统计数据展示")
    print("3. 🔍 强大的搜索和筛选功能")
    print("4. ⚡ 批量操作支持")
    print("5. 📱 响应式设计，支持移动端")
    print("6. 🎯 快速模板功能")
    print("7. 📋 一键复制兑换码")
    print("8. 🔄 实时状态更新")

if __name__ == '__main__':
    test_redemption_management()
